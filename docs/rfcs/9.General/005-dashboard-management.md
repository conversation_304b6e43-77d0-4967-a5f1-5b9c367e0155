# RFC 005: Dashboard Management

## Özet
Bu RFC, MasterCRM sistemi içerisinde yönetilebilir dashboard ve widget yapısının General mod<PERSON><PERSON><PERSON> altında nasıl implement edileceğini açıklar. Kullanıcılar birden fazla dashboard oluşturabilir, bu dashboardlar üzerine widget'lar ekleyebilir ve her biri için izin yapılandırmaları tanımlayabilir.

## Motivasyon
- Kullanıcıların kişiselleştirilebilir dashboard'lar oluşturma ihtiyacı
- Widget bazlı modüler dashboard yapısı
- Dashboard ve widget seviyesinde izin yönetimi
- Dinamik dashboard konfigürasyonları

## Teknik Tasarım

### Entity Modeli

#### Dashboard Entity
```csharp
public class Dashboard : BaseEntity
{
    public string Title { get; private set; }
    public string? Description { get; private set; }
    public bool IsActive { get; private set; }
    public string? Configuration { get; private set; } // JSON format
    public string? Permissions { get; private set; } // JSON format
    public ICollection<Widget> Widgets { get; private set; } = [];

    private Dashboard() { } // EF Constructor

    public Dashboard(string title, string? description = null)
    {
        Title = title;
        Description = description;
        IsActive = true;
    }

    public void UpdateTitle(string title) => Title = title;
    public void UpdateDescription(string? description) => Description = description;
    public void SetConfiguration(string? configuration) => Configuration = configuration;
    public void SetPermissions(string? permissions) => Permissions = permissions;
    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}
```

#### Widget Entity
```csharp
public class Widget : BaseEntity
{
    public Guid DashboardId { get; private set; }
    public string Title { get; private set; }
    public string Type { get; private set; }
    public int Order { get; private set; }
    public bool IsActive { get; private set; }
    public string? Configuration { get; private set; } // JSON format
    public string? Permissions { get; private set; } // JSON format
    public Dashboard Dashboard { get; private set; } = null!;

    private Widget() { } // EF Constructor

    public Widget(Guid dashboardId, string title, string type, int order = 0)
    {
        DashboardId = dashboardId;
        Title = title;
        Type = type;
        Order = order;
        IsActive = true;
    }

    public void UpdateTitle(string title) => Title = title;
    public void UpdateType(string type) => Type = type;
    public void UpdateOrder(int order) => Order = order;
    public void SetConfiguration(string? configuration) => Configuration = configuration;
    public void SetPermissions(string? permissions) => Permissions = permissions;
    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}
```

### Database Context

```csharp
public interface IGeneralDbContext : IDbContext
{
    DbSet<Dashboard> Dashboards { get; }
    DbSet<Widget> Widgets { get; }
}
```

### Database Schema

```sql
-- General modülü altında dashboard tabloları
CREATE TABLE General.Dashboards (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    IsActive BIT NOT NULL DEFAULT 1,
    Configuration NVARCHAR(MAX),
    Permissions NVARCHAR(MAX),
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX)
);

CREATE TABLE General.Widgets (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    DashboardId UNIQUEIDENTIFIER NOT NULL,
    Title NVARCHAR(200) NOT NULL,
    Type NVARCHAR(100) NOT NULL,
    [Order] INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    Configuration NVARCHAR(MAX),
    Permissions NVARCHAR(MAX),
    InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX),
    FOREIGN KEY (DashboardId) REFERENCES General.Dashboards(Id) ON DELETE CASCADE
);

-- Index'ler
CREATE INDEX IX_Widgets_DashboardId ON General.Widgets(DashboardId);
CREATE INDEX IX_Dashboards_IsActive ON General.Dashboards(IsActive);
CREATE INDEX IX_Widgets_IsActive ON General.Widgets(IsActive);
```

## Klasör Yapısı

```
src/
├── Modules/
│   └── General/
│       └── Application/
│           ├── Dashboards/
│           │   ├── CreateDashboard/
│           │   │   ├── CreateDashboardCommand.cs
│           │   │   ├── CreateDashboardCommandHandler.cs
│           │   │   ├── CreateDashboardCommandValidator.cs
│           │   │   └── CreateDashboardEndpoint.cs
│           │   ├── GetDashboard/
│           │   │   ├── GetDashboardQuery.cs
│           │   │   ├── GetDashboardQueryHandler.cs
│           │   │   └── GetDashboardEndpoint.cs
│           │   ├── UpdateDashboard/
│           │   ├── DeleteDashboard/
│           │   └── ListDashboards/
│           └── Widgets/
│               ├── CreateWidget/
│               │   ├── CreateWidgetCommand.cs
│               │   ├── CreateWidgetCommandHandler.cs
│               │   ├── CreateWidgetCommandValidator.cs
│               │   └── CreateWidgetEndpoint.cs
│               ├── GetWidget/
│               │   ├── GetWidgetQuery.cs
│               │   ├── GetWidgetQueryHandler.cs
│               │   └── GetWidgetEndpoint.cs
│               ├── UpdateWidget/
│               ├── DeleteWidget/
│               └── ListWidgets/
```

## CQRS Implementation Örnekleri

### Dashboard Commands/Queries

#### CreateDashboardCommand
```csharp
public record CreateDashboardCommand(
    string Title,
    string? Description,
    string? Configuration,
    string? Permissions
) : ICommand<Result<Guid>>;

public class CreateDashboardCommandHandler(IGeneralDbContext context)
    : ICommandHandler<CreateDashboardCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateDashboardCommand command, CancellationToken cancellationToken)
    {
        var dashboard = new Dashboard(command.Title, command.Description);

        if (!string.IsNullOrEmpty(command.Configuration))
            dashboard.SetConfiguration(command.Configuration);

        if (!string.IsNullOrEmpty(command.Permissions))
            dashboard.SetPermissions(command.Permissions);

        context.Dashboards.Add(dashboard);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(dashboard.Id);
    }
}

public class CreateDashboardCommandValidator : AbstractValidator<CreateDashboardCommand>
{
    public CreateDashboardCommandValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .MaximumLength(200);

        RuleFor(x => x.Description)
            .MaximumLength(500);
    }
}
```

#### GetDashboardQuery
```csharp
public record GetDashboardQuery(Guid Id) : IQuery<Result<DashboardDto>>;

public class GetDashboardQueryHandler(IGeneralDbContext context)
    : IQueryHandler<GetDashboardQuery, Result<DashboardDto>>
{
    public async Task<Result<DashboardDto>> Handle(GetDashboardQuery query, CancellationToken cancellationToken)
    {
        var dashboard = await context.Dashboards
            .Include(d => d.Widgets)
            .FirstOrDefaultAsync(d => d.Id == query.Id, cancellationToken);

        if (dashboard == null)
            return Result.Failure<DashboardDto>(new Error("Dashboard.NotFound", "Dashboard bulunamadı."));

        var dto = new DashboardDto(
            dashboard.Id,
            dashboard.Title,
            dashboard.Description,
            dashboard.IsActive,
            dashboard.Configuration,
            dashboard.Permissions,
            dashboard.Widgets.Select(w => new WidgetDto(
                w.Id,
                w.DashboardId,
                w.Title,
                w.Type,
                w.Order,
                w.IsActive,
                w.Configuration,
                w.Permissions
            )).ToList()
        );

        return Result.Success(dto);
    }
}
```

### Widget Commands/Queries

#### CreateWidgetCommand
```csharp
public record CreateWidgetCommand(
    Guid DashboardId,
    string Title,
    string Type,
    int Order,
    string? Configuration,
    string? Permissions
) : ICommand<Result<Guid>>;

public class CreateWidgetCommandHandler(IGeneralDbContext context)
    : ICommandHandler<CreateWidgetCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateWidgetCommand command, CancellationToken cancellationToken)
    {
        var dashboardExists = await context.Dashboards
            .AnyAsync(d => d.Id == command.DashboardId && d.IsActive, cancellationToken);

        if (!dashboardExists)
            return Result.Failure<Guid>(new Error("Dashboard.NotFound", "Dashboard bulunamadı."));

        var widget = new Widget(command.DashboardId, command.Title, command.Type, command.Order);

        if (!string.IsNullOrEmpty(command.Configuration))
            widget.SetConfiguration(command.Configuration);

        if (!string.IsNullOrEmpty(command.Permissions))
            widget.SetPermissions(command.Permissions);

        context.Widgets.Add(widget);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(widget.Id);
    }
}

public class CreateWidgetCommandValidator : AbstractValidator<CreateWidgetCommand>
{
    public CreateWidgetCommandValidator()
    {
        RuleFor(x => x.DashboardId)
            .NotEmpty();

        RuleFor(x => x.Title)
            .NotEmpty()
            .MaximumLength(200);

        RuleFor(x => x.Type)
            .NotEmpty()
            .MaximumLength(100);

        RuleFor(x => x.Order)
            .GreaterThanOrEqualTo(0);
    }
}
```

#### GetWidgetQuery
```csharp
public record GetWidgetQuery(Guid Id) : IQuery<Result<WidgetDto>>;

public class GetWidgetQueryHandler(IGeneralDbContext context)
    : IQueryHandler<GetWidgetQuery, Result<WidgetDto>>
{
    public async Task<Result<WidgetDto>> Handle(GetWidgetQuery query, CancellationToken cancellationToken)
    {
        var widget = await context.Widgets
            .FirstOrDefaultAsync(w => w.Id == query.Id, cancellationToken);

        if (widget == null)
            return Result.Failure<WidgetDto>(new Error("Widget.NotFound", "Widget bulunamadı."));

        var dto = new WidgetDto(
            widget.Id,
            widget.DashboardId,
            widget.Title,
            widget.Type,
            widget.Order,
            widget.IsActive,
            widget.Configuration,
            widget.Permissions
        );

        return Result.Success(dto);
    }
}
```

## DTOs

```csharp
public record DashboardDto(
    Guid Id,
    string Title,
    string? Description,
    bool IsActive,
    string? Configuration,
    string? Permissions,
    List<WidgetDto> Widgets
);

public record WidgetDto(
    Guid Id,
    Guid DashboardId,
    string Title,
    string Type,
    int Order,
    bool IsActive,
    string? Configuration,
    string? Permissions
);
```

## API Endpoints

### Dashboard Endpoints

#### CreateDashboardEndpoint
```csharp
public class CreateDashboardEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/general/dashboards", async (
                CreateDashboardRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var command = new CreateDashboardCommand(
                    request.Title,
                    request.Description,
                    request.Configuration,
                    request.Permissions
                );

                var result = await mediator.Send(command, cancellationToken);

                return result.IsSuccess
                    ? Results.Created($"/api/v1/general/dashboards/{result.Value}", result.Value)
                    : Results.BadRequest(result.Error);
            })
            .WithTags("General.Dashboards")
            .WithGroupName("apiv1")
            .Produces<Guid>(201)
            .Produces<Error>(400)
            .WithSummary("Dashboard oluştur")
            .WithDescription("Yeni bir dashboard oluşturur.");
    }
}

public record CreateDashboardRequest(
    string Title,
    string? Description,
    string? Configuration,
    string? Permissions
);
```

### Widget Endpoints

#### CreateWidgetEndpoint
```csharp
public class CreateWidgetEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/general/widgets", async (
                CreateWidgetRequest request,
                IMediator mediator,
                CancellationToken cancellationToken) =>
            {
                var command = new CreateWidgetCommand(
                    request.DashboardId,
                    request.Title,
                    request.Type,
                    request.Order,
                    request.Configuration,
                    request.Permissions
                );

                var result = await mediator.Send(command, cancellationToken);

                return result.IsSuccess
                    ? Results.Created($"/api/v1/general/widgets/{result.Value}", result.Value)
                    : Results.BadRequest(result.Error);
            })
            .WithTags("General.Widgets")
            .WithGroupName("apiv1")
            .Produces<Guid>(201)
            .Produces<Error>(400)
            .WithSummary("Widget oluştur")
            .WithDescription("Bir dashboard'a yeni widget ekler.");
    }
}

public record CreateWidgetRequest(
    Guid DashboardId,
    string Title,
    string Type,
    int Order,
    string? Configuration,
    string? Permissions
);
```

## Konfigürasyon Örnekleri

### Dashboard Configuration JSON
```json
{
  "layout": "grid",
  "columns": 3,
  "refreshInterval": 30000,
  "theme": "default",
  "autoRefresh": true
}
```

### Widget Configuration JSON
```json
{
  "chartType": "line",
  "dataSource": "sales_data",
  "refreshRate": 15000,
  "height": 300,
  "width": 400,
  "filters": {
    "dateRange": "last30days",
    "department": "sales"
  }
}
```

### Permissions JSON
```json
{
  "read": ["admin", "manager", "user"],
  "write": ["admin", "manager"],
  "delete": ["admin"]
}
```

## Başarı Kriterleri

- Dashboard CRUD işlemleri çalışır durumda
- Widget CRUD işlemleri çalışır durumda
- JSON konfigürasyonları doğru saklanır
- İzin yapıları implement edilir
- Entity ilişkileri doğru çalışır
- API endpoints doğru response'lar döner

## Riskler ve Önlemler

1. **JSON Validation**: Configuration JSON'larının geçerliliği kontrol edilmeli
   - Çözüm: FluentValidation ile JSON schema validation

2. **Performance**: Çok sayıda widget içeren dashboard'lar yavaş olabilir
   - Çözüm: Lazy loading ve pagination implementasyonu

3. **İzin Karmaşıklığı**: İç içe izin yapıları karmaşık olabilir
   - Çözüm: Basit role-based access control ile başlanmalı

## Alternatifler

1. **NoSQL Configuration**: JSON yapıları için ayrı NoSQL database
   - Karar: SQL Server JSON support yeterli

2. **Modül Ayrımı**: Dashboard'lar için ayrı modül
   - Karar: General modülü altında tutulması daha uygun

## Sonuç

Bu RFC ile MasterCRM sistemi içerisinde esnek ve yönetilebilir bir dashboard yapısı kurulacaktır. Kullanıcılar kendi dashboard'larını oluşturup, widget'lar ekleyip yapılandırabileceklerdir.